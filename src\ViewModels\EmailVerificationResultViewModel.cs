using RazeWinComTr.Areas.Admin.Enums;

namespace RazeWinComTr.ViewModels;

/// <summary>
/// Generic view model for verification result page (Email, Phone, Address, etc.)
/// </summary>
public class VerificationResultViewModel
{
    /// <summary>
    /// Whether the verification was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Title to display on the result page
    /// </summary>
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Message to display on the result page
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Whether to show the login button
    /// </summary>
    public bool ShowLoginButton { get; set; }

    /// <summary>
    /// Type of verification that was performed
    /// </summary>
    public VerificationType VerificationType { get; set; }
}

/// <summary>
/// Email-specific verification result view model for backward compatibility
/// </summary>
public class EmailVerificationResultViewModel : VerificationResultViewModel
{
    public EmailVerificationResultViewModel()
    {
        VerificationType = VerificationType.Email;
    }
}
