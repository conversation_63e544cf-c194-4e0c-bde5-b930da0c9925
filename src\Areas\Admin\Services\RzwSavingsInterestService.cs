using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services.Interfaces;

namespace RazeWinComTr.Areas.Admin.Services;

/// <summary>
/// Service for calculating and processing RZW savings interest
/// </summary>
public class RzwSavingsInterestService : IRzwSavingsInterestService
{
    private readonly AppDbContext _context;
    private readonly IRzwWalletBalanceManagementService _balanceService;
    private readonly IRzwSavingsPlanService _planService;
    private readonly ITokenPriceService _tokenPriceService;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly ILogger<RzwSavingsInterestService> _logger;

    public RzwSavingsInterestService(
        AppDbContext context,
        IRzwWalletBalanceManagementService balanceService,
        IRzwSavingsPlanService planService,
        ITokenPriceService tokenPriceService,
        IStringLocalizer<SharedResource> localizer,
        ILogger<RzwSavingsInterestService> logger)
    {
        _context = context;
        _balanceService = balanceService;
        _planService = planService;
        _tokenPriceService = tokenPriceService;
        _localizer = localizer;
        _logger = logger;
    }

    /// <summary>
    /// Processes daily interest for all eligible accounts
    /// </summary>
    /// <returns>Number of processed accounts and total interest paid</returns>
    public async Task<(int ProcessedAccounts, decimal TotalInterestPaid)> ProcessDailyInterestAsync()
    {
        var processedAccounts = 0;
        var totalInterestPaid = 0m;

        try
        {
            var accounts = await GetAccountsForDailyInterestAsync();

            foreach (var account in accounts)
            {
                var interestAmount = await CalculateDailyInterestAsync(account);

                if (interestAmount > 0)
                {
                    var success = await PayInterestAsync(account, interestAmount);
                    if (success)
                    {
                        processedAccounts++;
                        totalInterestPaid += interestAmount;
                    }
                }
            }

            _logger.LogInformation("Daily interest processing completed. Accounts: {Accounts}, Total Interest: {Interest}",
                processedAccounts, totalInterestPaid);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during daily interest processing");
        }

        return (processedAccounts, totalInterestPaid);
    }

    /// <summary>
    /// Calculates daily compound interest for a savings account
    /// </summary>
    /// <param name="account">The savings account</param>
    /// <returns>Daily interest amount</returns>
    public async Task<decimal> CalculateDailyInterestAsync(RzwSavingsAccount account)
    {
        if (account.Status != RzwSavingsStatus.Active) return 0;

        var rzwSavingsAccountIsValid = await _context.RzwSavingsAccounts.AnyAsync(a => a.Id == account.Id && a.Status == RzwSavingsStatus.Active);
        if (!rzwSavingsAccountIsValid)
        {
            _logger.LogWarning("Invalid savings account for daily interest calculation. AccountId: {AccountId}, Status: {Status}",
                account.Id, account.Status);
            return 0; // Invalid account or not active
        }
        //var plan = await _context.RzwSavingsPlans.FindAsync(account.PlanId);
        //if (plan == null || !plan.IsActive) return 0;

        // Calculate daily rate based on plan type using helper class
        var annualRate = account.InterestRate;
        var dailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(annualRate, account.TermType);

        // Current principal (initial amount + earned interest)
        var currentPrincipal = account.RzwAmount + account.TotalEarnedRzw;

        // Daily compound interest = Current principal * Daily rate
        var dailyInterest = currentPrincipal * dailyRate;

        return Math.Round(dailyInterest, 8, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Calculates interest for early withdrawal based on holding period
    /// </summary>
    /// <param name="account">The savings account</param>
    /// <returns>Earned interest amount</returns>
    public async Task<decimal> CalculateEarlyWithdrawalInterestAsync(RzwSavingsAccount account)
    {
        if (account.Status != RzwSavingsStatus.Active) return 0;

        var heldDays = (DateTime.UtcNow - account.StartDate).Days;
        if (heldDays <= 0) return 0; // Not held at all

        // Find eligible plan for early withdrawal based on holding period
        var eligiblePlan = await FindEligiblePlanForEarlyWithdrawalAsync(heldDays);

        if (eligiblePlan == null)
        {
            _logger.LogInformation("No eligible plan found for early withdrawal. AccountId: {AccountId}, HeldDays: {HeldDays}",
                account.Id, heldDays);
            return 0; // No eligible plan, no interest
        }

        // Calculate interest using eligible plan's rate using helper class
        var dailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(eligiblePlan.InterestRate, eligiblePlan.TermType);
        var totalInterest = CalculateCompoundInterest(account.RzwAmount, dailyRate, heldDays);

        _logger.LogInformation("Early withdrawal interest calculated. AccountId: {AccountId}, HeldDays: {HeldDays}, EligiblePlan: {PlanName}, Interest: {Interest}",
            account.Id, heldDays, eligiblePlan.Name, totalInterest);

        return Math.Round(totalInterest, 8, MidpointRounding.AwayFromZero);
    }

    /// <summary>
    /// Finds eligible plan for early withdrawal based on holding period
    /// </summary>
    /// <param name="heldDays">Number of days held</param>
    /// <returns>Eligible plan or null</returns>
    private async Task<RzwSavingsPlan?> FindEligiblePlanForEarlyWithdrawalAsync(int heldDays)
    {
        var activePlans = await _planService.GetActivePlansAsync();

        // Filter plans that have term duration <= held days
        var eligiblePlans = activePlans
            .Where(p => p.TermDuration <= heldDays) // Term duration shorter or equal to holding period
            .OrderByDescending(p => p.TermDuration) // Select the longest term (highest interest)
            .ToList();

        return eligiblePlans.FirstOrDefault();
    }

    /// <summary>
    /// Calculates compound interest using the shared helper class for consistency
    /// </summary>
    /// <param name="principal">Principal amount</param>
    /// <param name="dailyRate">Daily interest rate</param>
    /// <param name="days">Number of days</param>
    /// <returns>Compound interest amount</returns>
    public decimal CalculateCompoundInterest(decimal principal, decimal dailyRate, int days)
    {
        // Use the shared helper class for consistent calculations
        return RzwSavingsCalculationHelper.CalculateCompoundInterest(principal, dailyRate, days);
    }

    /// <summary>
    /// Gets accounts that need daily interest processing
    /// </summary>
    /// <returns>List of accounts for interest processing</returns>
    public async Task<List<RzwSavingsAccount>> GetAccountsForDailyInterestAsync()
    {
        var today = DateTime.UtcNow.Date;

        return await _context.RzwSavingsAccounts
            .Where(s => s.Status == RzwSavingsStatus.Active &&
                       (s.LastInterestDate == null || s.LastInterestDate.Value.Date < today) &&
                       s.StartDate.Date <= today)
            .ToListAsync();
    }

    /// <summary>
    /// Pays interest to a savings account
    /// </summary>
    /// <param name="account">The savings account</param>
    /// <param name="interestAmount">Interest amount to pay</param>
    /// <returns>True if successful, false otherwise</returns>
    public async Task<bool> PayInterestAsync(RzwSavingsAccount account, decimal interestAmount)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Add interest to available balance
            var wallet = await _balanceService.AddRzwInterestAsync(account.Id, account.UserId, interestAmount,
                $"Daily interest from savings account #{account.Id}", _context);

            if (wallet == null) return false;

            // Create interest payment record
            var interestPayment = new RzwSavingsInterestPayment
            {
                RzwSavingsAccountId = account.Id,
                RzwAmount = interestAmount,
                PaymentDate = DateTime.UtcNow,
                Description = $"Daily interest payment - {account.TermType} savings",
                CreatedDate = DateTime.UtcNow,
                // Set principal and accumulated interest for balance calculation
                PrincipalAmount = account.RzwAmount,
                AccumulatedInterest = account.TotalEarnedRzw + interestAmount,
                DailyRate = RzwSavingsCalculationHelper.CalculateDailyRate(account.InterestRate, account.TermType)
            };

            _context.RzwSavingsInterestPayments.Add(interestPayment);

            // Update account information
            account.TotalEarnedRzw += interestAmount;
            account.LastInterestDate = DateTime.UtcNow;
            account.ModifiedDate = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            await transaction.CommitAsync();

            _logger.LogDebug("Interest paid. AccountId: {AccountId}, UserId: {UserId}, Amount: {Amount}",
                account.Id, account.UserId, interestAmount);

            return true;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            _logger.LogError(ex, "Error paying interest. AccountId: {AccountId}, Amount: {Amount}",
                account.Id, interestAmount);
            return false;
        }
    }

    /// <summary>
    /// Gets user's interest payment history
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="pageSize">Page size</param>
    /// <param name="page">Page number</param>
    /// <returns>List of interest payments</returns>
    public async Task<List<RzwSavingsInterestPayment>> GetUserInterestHistoryAsync(int userId, int pageSize = 50, int page = 1)
    {
        return await _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .Where(p => p.RzwSavingsAccount.UserId == userId)
            .OrderByDescending(p => p.PaymentDate)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    /// <summary>
    /// Gets total earned interest for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <returns>Total earned interest amount</returns>
    public async Task<decimal> GetUserTotalEarnedInterestAsync(int userId)
    {
        return await _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .Where(p => p.RzwSavingsAccount.UserId == userId)
            .SumAsync(p => p.RzwAmount);
    }

    /// <summary>
    /// Gets interest payment history for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="accountId">Optional account ID filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <param name="pageNumber">Page number for pagination</param>
    /// <param name="pageSize">Page size for pagination</param>
    /// <returns>List of interest payments</returns>
    public async Task<List<RzwSavingsInterestPayment>> GetInterestHistoryAsync(
        int userId,
        int? accountId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null,
        int pageNumber = 1,
        int pageSize = 50)
    {
        var query = _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .ThenInclude(a => a.Plan)
            .Where(p => p.RzwSavingsAccount.UserId == userId);

        if (accountId.HasValue)
        {
            query = query.Where(p => p.RzwSavingsAccountId == accountId.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(p => p.PaymentDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(p => p.PaymentDate <= toDate.Value);
        }

        return await query
            .OrderByDescending(p => p.PaymentDate)
            .Skip((pageNumber - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }

    /// <summary>
    /// Gets interest payment history count for a user
    /// </summary>
    /// <param name="userId">User ID</param>
    /// <param name="accountId">Optional account ID filter</param>
    /// <param name="fromDate">Optional from date filter</param>
    /// <param name="toDate">Optional to date filter</param>
    /// <returns>Total count of interest payments</returns>
    public async Task<int> GetInterestHistoryCountAsync(
        int userId,
        int? accountId = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        var query = _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .Where(p => p.RzwSavingsAccount.UserId == userId);

        if (accountId.HasValue)
        {
            query = query.Where(p => p.RzwSavingsAccountId == accountId.Value);
        }

        if (fromDate.HasValue)
        {
            query = query.Where(p => p.PaymentDate >= fromDate.Value);
        }

        if (toDate.HasValue)
        {
            query = query.Where(p => p.PaymentDate <= toDate.Value);
        }

        return await query.CountAsync();
    }

    /// <summary>
    /// Gets interest payment history for a specific account
    /// </summary>
    /// <param name="accountId">Account ID</param>
    /// <param name="userId">User ID for security check</param>
    /// <returns>List of interest payments for the account</returns>
    public async Task<List<RzwSavingsInterestPayment>> GetAccountInterestHistoryAsync(int accountId, int userId)
    {
        return await _context.RzwSavingsInterestPayments
            .Include(p => p.RzwSavingsAccount)
            .Where(p => p.RzwSavingsAccountId == accountId && p.RzwSavingsAccount.UserId == userId)
            .OrderByDescending(p => p.PaymentDate)
            .ToListAsync();
    }
}
