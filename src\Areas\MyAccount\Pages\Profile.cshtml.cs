using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using RazeWinComTr.Areas.Admin.DbModel;
using RazeWinComTr.Areas.Admin.Helpers;
using RazeWinComTr.Areas.Admin.Services;
using RazeWinComTr.Areas.Admin.Services.Interfaces;
using RazeWinComTr.Areas.Admin.ViewModels;
using RazeWinComTr.Areas.Admin.ViewModels.Referral;
using RazeWinComTr.Areas.MyAccount.ViewModels;
using RazeWinComTr.Attributes;
using System.ComponentModel.DataAnnotations;
using Microsoft.Extensions.Logging;

namespace RazeWinComTr.Areas.MyAccount.Pages;

public class ProfileModel : PageModel
{
    private readonly IStringLocalizer<RazeWinComTr.SharedResource> _localizer;
    private readonly AppDbContext _context;
    private readonly ReferralService _referralService;
    private readonly ILogger<ProfileModel> _logger;
    private readonly IHttpContextHelper _httpContextHelper;
    private readonly IWebHostEnvironment _environment;
    private readonly EmailVerificationService _emailVerificationService;

    public ProfileModel(
        AppDbContext context,
        IStringLocalizer<SharedResource> localizer,
        ReferralService referralService,
        ILogger<ProfileModel> logger,
        IHttpContextHelper httpContextHelper,
        IWebHostEnvironment environment,
        EmailVerificationService emailVerificationService)
    {
        _localizer = localizer;
        _context = context;
        _referralService = referralService;
        _logger = logger;
        _httpContextHelper = httpContextHelper;
        _environment = environment;
        _emailVerificationService = emailVerificationService;
        UserData = new User();
        ProfileInput = new ProfileInputModel();
        PasswordInput = new PasswordInputModel();
    }

    public User? UserData { get; set; }

    public string UserFullName { get; set; } = string.Empty;
    public string UserEmail { get; set; } = string.Empty;
    public string UserPhone { get; set; } = string.Empty;
    public DateTime UserCreatedDate { get; set; }
    public string UserReferralCode { get; set; } = string.Empty;
    public string UserReferralLink { get; set; } = string.Empty;
    public List<User> ReferredUsers { get; set; } = new List<User>();
    public List<ReferralHierarchyItem> ReferralHierarchy { get; set; } = new List<ReferralHierarchyItem>();
    public int ReferredUsersCount { get; set; }
    public int? InviteLimit { get; set; }
    public int? RemainingInvites { get; set; }
    public bool HasUnlimitedInvites { get; set; }

    // Email verification view model
    public EmailVerificationViewModel EmailVerification { get; set; } = new();

    [BindProperty]
    public ProfileInputModel ProfileInput { get; set; }

    [BindProperty]
    public PasswordInputModel PasswordInput { get; set; }

    public SweetAlert2Message? AlertMessage { get; private set; }

    public string? ActiveTab { get; set; } = "profileTab";


    public async Task<IActionResult> OnGetAsync()
    {
        var userId = HttpContext.User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return RedirectToPage("/Account/Login", new { area = "Identity" });
        }

        UserData = await _context.Users.FirstOrDefaultAsync(m => m.UserId == userId) ?? new User();

        // Load referred users
        ReferredUsers = await _context.Users
            .Where(u => u.ReferrerId == userId)
            .ToListAsync();

        ReferredUsersCount = ReferredUsers.Count;

        // Load referral hierarchy
        ReferralHierarchy = await _referralService.GetReferralHierarchyAsync(userId.Value);

        // Get invite limit information
        InviteLimit = await _referralService.GetInviteLimitAsync(userId.Value);
        RemainingInvites = await _referralService.GetRemainingInvitesAsync(userId.Value);
        HasUnlimitedInvites = !InviteLimit.HasValue;

        // Initialize the input model with user data
        ProfileInput = new ProfileInputModel
        {
            Email = UserData.Email,
            Name = UserData.Name,
            Surname = UserData.Surname,
            PhoneNumber = UserData.PhoneNumber,
            IdentityNumber = UserData.IdentityNumber,
            BirthDate = UserData.BirthDate,
            Iban = UserData.Iban
        };

        UserFullName = $"{UserData.Name} {UserData.Surname}";
        UserEmail = UserData.Email;
        UserPhone = UserData.PhoneNumber;
        UserCreatedDate = UserData.CrDate;
        UserReferralCode = UserData.ReferralCode;

        // Generate the referral link based on the environment
        string baseUrl = _httpContextHelper.GetBaseUrl();
        UserReferralLink = $"{baseUrl}/Register?referenceCode={UserReferralCode}";

        // Load email verification status
        var emailStatus = await _emailVerificationService.GetVerificationStatusAsync(userId.Value);
        EmailVerification.IsVerified = emailStatus.IsVerified;
        EmailVerification.VerifiedDate = emailStatus.VerifiedDate;
        EmailVerification.CanSendVerification = emailStatus.CanSendVerification;
        EmailVerification.TimeUntilNext = emailStatus.TimeUntilNextVerification;
        EmailVerification.TodayAttempts = emailStatus.TodayAttempts;
        EmailVerification.MaxDailyAttempts = emailStatus.MaxDailyAttempts;
        EmailVerification.MinutesBetweenAttempts = emailStatus.MinutesBetweenAttempts;
        EmailVerification.EmailAddress = UserData.Email;

        return Page();
    }

    public async Task<IActionResult> OnPostSaveProfile()
    {
        ActiveTab = "profileTab";//it is profileTab if this method is called.

        var userId = HttpContext.User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Only validate the ProfileInput model, not the PasswordInput model
        var profileModelState = new ModelStateDictionary();
        foreach (var key in ModelState.Keys.Where(k => k.StartsWith("ProfileInput.")))
        {
            var entry = ModelState[key];
            if (entry != null && entry.Errors.Count > 0)
            {
                foreach (var error in entry.Errors)
                {
                    profileModelState.AddModelError(key, error.ErrorMessage);
                }
            }
        }

        if (!profileModelState.IsValid)
        {
            // Copy the profile validation errors to the main ModelState
            foreach (var item in profileModelState)
            {
                ModelState.SetModelValue(item.Key, item.Value.RawValue, item.Value.AttemptedValue);
            }
            await OnGetAsync();
            // Set the active tab to profile
            return Page();
        }

        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }

        // Convert local date to UTC for storage
        var birthDate = DateTimeFormatHelper.ConvertToUtc(ProfileInput.BirthDate);

        // Update user data
        user.Name = ProfileInput.Name;
        user.Surname = ProfileInput.Surname;
        user.PhoneNumber = ProfileInput.PhoneNumber;
        user.IdentityNumber = ProfileInput.IdentityNumber;
        user.BirthDate = birthDate;
        user.Iban = ProfileInput.Iban;
        user.ModDate = DateTime.UtcNow;

        try
        {
            await _context.SaveChangesAsync();

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/MyAccount/Profile"
            };
            await OnGetAsync();
            return Page();
        }
        catch (Exception ex)
        {
            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = $"Error saving profile: {ex.Message}",
                Icon = "error",
                RedirectUrl = "/MyAccount/Profile"
            };
        }
        await OnGetAsync();
        return Page();
    }

    public async Task<IActionResult> OnPostChangePassword()
    {
        ActiveTab = "passwordTab";//it is passwordTab if this method is called.

        var userId = HttpContext.User.GetClaimUserId();
        if (!userId.HasValue)
        {
            return Unauthorized();
        }

        // Only validate the PasswordInput model, not the ProfileInput model
        var passwordModelState = new ModelStateDictionary();
        foreach (var key in ModelState.Keys.Where(k => k.StartsWith("PasswordInput.")))
        {
            var entry = ModelState[key];
            if (entry != null && entry.Errors.Count > 0)
            {
                foreach (var error in entry.Errors)
                {
                    passwordModelState.AddModelError(key, error.ErrorMessage);
                }
            }
        }

        if (!passwordModelState.IsValid)
        {
            // Copy the password validation errors to the main ModelState
            foreach (var item in passwordModelState)
            {
                ModelState.SetModelValue(item.Key, item.Value.RawValue, item.Value.AttemptedValue);
            }
            await OnGetAsync();
            return Page();
        }

        var user = await _context.Users.FirstOrDefaultAsync(u => u.UserId == userId);
        if (user == null)
        {
            return NotFound();
        }

        // Verify current password
        var currentPasswordHash = HashHelper.getHash(PasswordInput.CurrentPassword);
        if (user.PasswordHash != currentPasswordHash)
        {
            ModelState.AddModelError("PasswordInput.CurrentPassword", _localizer["Current password is incorrect"]);
            await OnGetAsync();
            return Page();
        }

        // Update password
        user.PasswordHash = HashHelper.getHash(PasswordInput.NewPassword);
        user.ModDate = DateTime.UtcNow;

        try
        {
            await _context.SaveChangesAsync();

            AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Success"],
                Text = _localizer["Successfully saved"],
                Icon = "success",
                RedirectUrl = "/MyAccount/Profile"
            };
            await OnGetAsync();
            return Page();
        }
        catch (Exception ex)
        {
              AlertMessage = new SweetAlert2Message
            {
                Title = _localizer["Error"],
                Text = $"Error changing password: {ex.Message}",
                Icon = "error",
                RedirectUrl = "/MyAccount/Profile"
            };
        }
        await OnGetAsync();
        return Page();
    }

    public class ProfileInputModel
    {
        [LocalizedRequired]
        [EmailAddress]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        [LocalizedRequired]
        [StringLength(100)]
        [Display(Name = "Name")]
        public string Name { get; set; } = string.Empty;

        [LocalizedRequired]
        [StringLength(100)]
        [Display(Name = "Surname")]
        public string Surname { get; set; } = string.Empty;

        [LocalizedRequired]
        [StringLength(20)]
        [Display(Name = "Phone Number")]
        public string PhoneNumber { get; set; } = string.Empty;

        [LocalizedRequired]
        [StringLength(20)]
        [Display(Name = "Identity Number")]
        public string IdentityNumber { get; set; } = string.Empty;

        [LocalizedRequired]
        [DataType(DataType.Date)]
        [Display(Name = "Birth Date")]
        public DateTime BirthDate { get; set; }

        [StringLength(50)]
        [Iban]
        [Display(Name = "IBAN")]
        public string? Iban { get; set; }
    }

    public class PasswordInputModel
    {
        [LocalizedRequired]
        [DataType(DataType.Password)]
        [Display(Name = "Current Password")]
        public string CurrentPassword { get; set; } = string.Empty;

        [LocalizedRequired]
        [LocalizedMinLength(6)]
        [DataType(DataType.Password)]
        [Display(Name = "New Password")]
        public string NewPassword { get; set; } = string.Empty;

        [DataType(DataType.Password)]
        [LocalizedCompare(nameof(NewPassword))]
        [Display(Name = "Confirm Password")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    public async Task<IActionResult> OnPostSendVerificationEmailAsync()
    {
        var userId = HttpContext.User.GetClaimUserId();
        if (!userId.HasValue)
        {
            TempData["ErrorMessage"] = _localizer["User not found. Please log in again."];
            return RedirectToPage();
        }

        try
        {
            // Check if user can send verification email (rate limiting)
            var canSend = await _emailVerificationService.CanSendVerificationEmailAsync(userId.Value);
            if (!canSend)
            {
                var timeUntilNext = await _emailVerificationService.GetTimeUntilNextEmailAsync(userId.Value);
                if (timeUntilNext.HasValue)
                {
                    var minutes = (int)Math.Ceiling(timeUntilNext.Value.TotalMinutes);
                    TempData["ErrorMessage"] = _localizer["You can send another verification email in {0} minutes.", minutes];
                }
                else
                {
                    TempData["ErrorMessage"] = _localizer["You have reached the daily limit for verification emails. Please try again tomorrow."];
                }
                return RedirectToPage();
            }

            // Check if email is already verified
            var isVerified = await _emailVerificationService.IsEmailVerifiedAsync(userId.Value);
            if (isVerified)
            {
                TempData["ErrorMessage"] = _localizer["Your email is already verified."];
                return RedirectToPage();
            }

            // Get user data
            var user = await _context.Users.FindAsync(userId.Value);
            if (user == null)
            {
                TempData["ErrorMessage"] = _localizer["User not found."];
                return RedirectToPage();
            }

            // Send verification email
            var emailSent = await _emailVerificationService.SendVerificationEmailAsync(
                userId.Value,
                user.Email,
                $"{user.Name} {user.Surname}");

            if (emailSent)
            {
                TempData["SuccessMessage"] = _localizer["Verification email sent successfully. Please check your inbox."];
            }
            else
            {
                TempData["ErrorMessage"] = _localizer["Failed to send verification email. Please try again later."];
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification email for user {UserId}", userId.Value);
            TempData["ErrorMessage"] = _localizer["An error occurred while sending the verification email. Please try again later."];
        }

        return RedirectToPage();
    }
}
