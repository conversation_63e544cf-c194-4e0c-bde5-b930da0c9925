@page
@model RazeWinComTr.Areas.MyAccount.Pages.ProfileModel
@using Microsoft.Extensions.Localization
@using RazeWinComTr
@using RazeWinComTr.Areas.Admin.Helpers
@inject IStringLocalizer<SharedResource> Localizer
@{
    ViewData["Title"] = Localizer["My Profile"];
}

<link rel="stylesheet" href="~/site/pages/myaccount/profile/profile.css" />
@if (Model.AlertMessage != null)
{
    <script id="scriptMessage" type="text/javascript">
        window.onload = function () {
            Swal.fire({
                title: '@Html.Raw(Model.AlertMessage.Title)',
                text: '@Html.Raw(Model.AlertMessage.Text)',
                icon: '@Html.Raw(Model.AlertMessage.Icon)'
            }).then((result) => {
                var redirectUrl = '@(Model.AlertMessage.RedirectUrl ?? string.Empty)';
                if (redirectUrl !== '' && result.isConfirmed) {
                    location.href = redirectUrl;
                }
            });
            $('#scriptMessage').remove();
        }
    </script>
}
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">@Localizer["My Profile"]</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="/MyAccount/Dashboard">@Localizer["Home"]</a></li>
                    <li class="breadcrumb-item active">@Localizer["My Profile"]</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->
<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-3">
                <!-- Profile Image -->
                <div class="card card-primary card-outline">
                    <div class="card-body box-profile">
                        <div class="text-center">
                            <img class="profile-user-img img-fluid img-circle"
                                 src="/dist/img/user2-160x160.jpg"
                                 alt="User profile picture">
                        </div>

                        <h3 class="profile-username text-center">@(Model.UserData?.Name ?? string.Empty) @(Model.UserData?.Surname ?? string.Empty)</h3>

                        <p class="text-muted text-center">@(Model.UserData?.Email ?? string.Empty)</p>

                        <ul class="list-group list-group-unbordered mb-3">
                            <li class="list-group-item">
                                <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
                                    <b>@Localizer["Phone"]</b>
                                    <span class="mt-1 mt-sm-0">@(Model.UserData?.PhoneNumber ?? string.Empty)</span>
                                </div>
                            </li>
                            <li class="list-group-item">
                                <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
                                    <b>@Localizer["Member Since"]</b>
                                    <span class="mt-1 mt-sm-0">@DateTimeFormatHelper.FormatForDisplay(Model.UserData?.CrDate)</span>
                                </div>
                            </li>
                            @if (!string.IsNullOrEmpty(Model.UserData?.Iban))
                            {
                                <li class="list-group-item">
                                    <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
                                        <b>@Localizer["IBAN"]</b>
                                        <span class="mt-1 mt-sm-0 text-truncate" style="max-width: 180px;" title="@Model.UserData.Iban" id="sidebar-iban">@Model.UserData.Iban</span>
                                    </div>
                                </li>
                            }
                            <li class="list-group-item">
                                <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
                                    <b>@Localizer["Referral Code"]</b>
                                    <div class="d-flex align-items-center mt-1 mt-sm-0">
                                        <span id="sidebar-referral-code" class="mr-2 text-truncate" style="max-width: 120px;" title="@Model.UserReferralCode">@Model.UserReferralCode</span>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyToClipboard('sidebar-referral-code')" title="@Localizer["Copy"]">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </li>
                            <li class="list-group-item">
                                <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
                                    <b>@Localizer["Users Registered with Your Referral Code"]</b>
                                    <span class="mt-1 mt-sm-0 badge badge-primary">@Model.ReferredUsersCount</span>
                                </div>
                            </li>
                            <li class="list-group-item">
                                <div class="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center">
                                    <b>@Localizer["Invite Limit"]</b>
                                    @if (Model.HasUnlimitedInvites)
                                    {
                                        <span class="mt-1 mt-sm-0 badge badge-success">@Localizer["Unlimited"]</span>
                                    }
                                    else if (Model.InviteLimit.HasValue)
                                    {
                                        <span class="mt-1 mt-sm-0 badge badge-info">@Model.ReferredUsersCount / @Model.InviteLimit</span>
                                    }
                                </div>
                            </li>
                        </ul>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header p-2">
                        <ul class="nav nav-tabs">
                            <li class="nav-item"><a class="nav-link @(Model.ActiveTab == "profileTab" ? "active" : "")" href="#profileTab" data-toggle="tab">@Localizer["Profile"]</a></li>
                            <li class="nav-item"><a class="nav-link @(Model.ActiveTab == "passwordTab" ? "active" : "")" href="#passwordTab" data-toggle="tab">@Localizer["Change Password"]</a></li>
                            <li class="nav-item"><a class="nav-link @(Model.ActiveTab == "referralTab" ? "active" : "")" href="#referralTab" data-toggle="tab">@Localizer["Referral"]</a></li>
                        </ul>
                    </div><!-- /.card-header -->
                    <div class="card-body">
                        @if (TempData["SuccessMessage"] != null)
                        {
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                @TempData["SuccessMessage"]
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        }
                        @if (TempData["ErrorMessage"] != null)
                        {
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                @TempData["ErrorMessage"]
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                        }

                        <div class="tab-content">
                            <div class="@(Model.ActiveTab?.ToString() == "profileTab" ? "active" : "") tab-pane" id="profileTab">
                                <form method="post" asp-page-handler="SaveProfile" id="profileForm">
                                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.Email" class="col-sm-2 col-form-label">@Localizer["Email"]</label>
                                        <div class="col-sm-10">
                                            <div class="input-group">
                                                <input asp-for="ProfileInput.Email" class="form-control" readonly>
                                                @if (Model.EmailVerification.IsVerified)
                                                {
                                                    <div class="input-group-append">
                                                        <span class="input-group-text bg-success text-white" title="@Localizer["Email verified on"] @DateTimeFormatHelper.FormatForDisplay(Model.EmailVerification.VerifiedDate)">
                                                            <i class="@Model.EmailVerification.GetStatusIconClass()"></i> @Localizer["Verified"]
                                                        </span>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <div class="input-group-append">
                                                        <span class="input-group-text bg-warning text-dark">
                                                            <i class="@Model.EmailVerification.GetStatusIconClass()"></i> @Localizer["Not Verified"]
                                                        </span>
                                                    </div>
                                                }
                                            </div>
                                            <span asp-validation-for="ProfileInput.Email" class="text-danger"></span>
                                            @if (!Model.EmailVerification.IsVerified)
                                            {
                                                <div class="mt-2">
                                                    @if (Model.EmailVerification.CanSendVerification)
                                                    {
                                                        <form method="post" asp-page-handler="SendVerificationEmail" style="display: inline;">
                                                            <button type="submit" class="btn btn-sm btn-primary">
                                                                <i class="fas fa-envelope"></i> @Localizer["Send Verification Email"]
                                                            </button>
                                                        </form>
                                                    }
                                                    else
                                                    {
                                                        @if (Model.EmailVerification.TimeUntilNext.HasValue)
                                                        {
                                                            var minutes = (int)Math.Ceiling(Model.EmailVerification.TimeUntilNext.Value.TotalMinutes);
                                                            <small class="text-muted">
                                                                @Localizer["You can send another verification email in {0} minutes.", minutes]
                                                            </small>
                                                        }
                                                        else
                                                        {
                                                            <small class="text-muted">
                                                                @Localizer["You have reached the daily limit ({0}/{1}). Try again tomorrow.", Model.EmailVerification.TodayAttempts, Model.EmailVerification.MaxDailyAttempts]
                                                            </small>
                                                        }
                                                    }
                                                </div>
                                            }
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.Name" class="col-sm-2 col-form-label">@Localizer["Name"]</label>
                                        <div class="col-sm-10">
                                            <input asp-for="ProfileInput.Name" class="form-control">
                                            <span asp-validation-for="ProfileInput.Name" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.Surname" class="col-sm-2 col-form-label">@Localizer["Surname"]</label>
                                        <div class="col-sm-10">
                                            <input asp-for="ProfileInput.Surname" class="form-control">
                                            <span asp-validation-for="ProfileInput.Surname" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.PhoneNumber" class="col-sm-2 col-form-label">@Localizer["Phone Number"]</label>
                                        <div class="col-sm-10">
                                            <input asp-for="ProfileInput.PhoneNumber" class="form-control">
                                            <span asp-validation-for="ProfileInput.PhoneNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.IdentityNumber" class="col-sm-2 col-form-label">@Localizer["Identity Number"]</label>
                                        <div class="col-sm-10">
                                            <input asp-for="ProfileInput.IdentityNumber" class="form-control">
                                            <span asp-validation-for="ProfileInput.IdentityNumber" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.BirthDate" class="col-sm-2 col-form-label">@Localizer["Birth Date"]</label>
                                        <div class="col-sm-10">
                                            <input asp-for="ProfileInput.BirthDate" class="form-control" type="date" value="@(Model?.ProfileInput?.BirthDate.ToString("yyyy-MM-dd"))">
                                            <span asp-validation-for="ProfileInput.BirthDate" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="ProfileInput.Iban" class="col-sm-2 col-form-label">@Localizer["IBAN"]</label>
                                        <div class="col-sm-10">
                                            <input asp-for="ProfileInput.Iban" class="form-control iban-input" placeholder="TR00 0000 0000 0000 0000 0000 00">
                                            <span asp-validation-for="ProfileInput.Iban" class="text-danger"></span>
                                            <span id="ibanError" class="text-danger" style="display:none;">@Localizer["Enter a valid IBAN"]</span>
                                            <small class="form-text text-muted">@Localizer["Enter your bank account IBAN for withdrawals (e.g., TR00 0000 0000 0000 0000 0000 00)"]</small>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="offset-sm-2 col-sm-10">
                                            <button type="submit" class="btn btn-primary">@Localizer["Save Changes"]</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <!-- /.tab-pane -->
                            <div class="@(Model.ActiveTab?.ToString() == "passwordTab" ? "active" : "") tab-pane" id="passwordTab">
                                <form method="post" asp-page-handler="ChangePassword">
                                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                                    <div class="form-group row">
                                        <label asp-for="PasswordInput.CurrentPassword" class="col-sm-3 col-form-label">@Localizer["Current Password"]</label>
                                        <div class="col-sm-9">
                                            <input asp-for="PasswordInput.CurrentPassword" class="form-control" type="password">
                                            <span asp-validation-for="PasswordInput.CurrentPassword" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="PasswordInput.NewPassword" class="col-sm-3 col-form-label">@Localizer["New Password"]</label>
                                        <div class="col-sm-9">
                                            <input asp-for="PasswordInput.NewPassword" class="form-control" type="password">
                                            <span asp-validation-for="PasswordInput.NewPassword" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label asp-for="PasswordInput.ConfirmPassword" class="col-sm-3 col-form-label">@Localizer["Confirm Password"]</label>
                                        <div class="col-sm-9">
                                            <input asp-for="PasswordInput.ConfirmPassword" class="form-control" type="password">
                                            <span asp-validation-for="PasswordInput.ConfirmPassword" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <div class="offset-sm-3 col-sm-9">
                                            <button type="submit" class="btn btn-primary">@Localizer["Change Password"]</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <!-- /.tab-pane -->

                            <!-- Referral Tab -->
                            <div class="@(Model.ActiveTab?.ToString() == "referralTab" ? "active" : "") tab-pane" id="referralTab">
                                <div class="card">
                                    <div class="card-header">
                                        <h3 class="card-title">@Localizer["My Referral Code"]</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group">
                                            <label>@Localizer["Referral Code"]</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" value="@Model.UserReferralCode" readonly id="referralCodeInput">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-primary" onclick="copyToClipboard('referralCodeInput')" title="@Localizer["Copy"]">
                                                        <i class="fas fa-copy"></i> @Localizer["Copy"]
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">@Localizer["Share this code with friends to refer them to our platform."]</small>
                                        </div>

                                        <div class="form-group mt-4">
                                            <label>@Localizer["Referral Link"]</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" value="@Model.UserReferralLink" readonly id="referralLinkInput">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-primary" onclick="copyToClipboard('referralLinkInput')" title="@Localizer["Copy"]">
                                                        <i class="fas fa-copy"></i> @Localizer["Copy"]
                                                    </button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">@Localizer["Share this link with friends to directly register them with your referral code."]</small>
                                        </div>

                                        <div class="invite-limit-info mt-4">
                                            <h5>@Localizer["Invite Limit Information"]</h5>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="info-box bg-light">
                                                        <div class="info-box-content">
                                                            <span class="info-box-text">@Localizer["Current Invites"]</span>
                                                            <span class="info-box-number">@Model.ReferredUsersCount</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="info-box bg-light">
                                                        <div class="info-box-content">
                                                            <span class="info-box-text">@Localizer["Invite Limit"]</span>
                                                            @if (Model.HasUnlimitedInvites)
                                                            {
                                                                <span class="info-box-number text-success">@Localizer["Unlimited"]</span>
                                                            }
                                                            else if (Model.InviteLimit.HasValue)
                                                            {
                                                                <span class="info-box-number">@Model.InviteLimit</span>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            @if (Model.InviteLimit.HasValue && !Model.HasUnlimitedInvites)
                                            {
                                                <div class="progress-group mt-3">
                                                    <span class="progress-text">@Localizer["Invite Usage"]</span>
                                                    <span class="float-right">@Model.ReferredUsersCount / @Model.InviteLimit</span>
                                                    <div class="progress progress-sm">
                                                        @{
                                                            var percentage = Model.InviteLimit > 0 ? (Model.ReferredUsersCount * 100 / Model.InviteLimit) : 0;
                                                            var progressClass = percentage < 70 ? "bg-success" : (percentage < 90 ? "bg-warning" : "bg-danger");
                                                        }
                                                        <div class="progress-bar @progressClass" style="width: @percentage%"></div>
                                                    </div>
                                                </div>

                                                <div class="mt-2">
                                                    @if (Model.RemainingInvites.HasValue && Model.RemainingInvites.Value > 0)
                                                    {
                                                        <div class="alert alert-info">
                                                            <i class="fas fa-info-circle"></i> @Localizer["You can invite"] <strong>@Model.RemainingInvites</strong> @Localizer["more users with your current package."]
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-exclamation-triangle"></i> @Localizer["You have reached your invite limit. Upgrade your package to invite more users."]
                                                        </div>
                                                    }
                                                </div>
                                            }
                                            else if (Model.HasUnlimitedInvites)
                                            {
                                                <div class="alert alert-success mt-3">
                                                    <i class="fas fa-check-circle"></i> @Localizer["Your current package allows unlimited invites."]
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="alert alert-success mt-3">
                                                    <i class="fas fa-check-circle"></i> @Localizer["Your account allows unlimited invites."]
                                                    <a href="/MyAccount/Packages" class="btn btn-sm btn-primary ml-2">@Localizer["View Packages"]</a>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h3 class="card-title">@Localizer["Users Registered with Your Referral Code"]</h3>
                                    </div>
                                    <div class="card-body">
                                        @if (Model.ReferredUsers.Any())
                                        {
                                            <div class="table-responsive">
                                                <table class="table table-bordered table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>@Localizer["Name"]</th>
                                                            <th>@Localizer["Email"]</th>
                                                            <th>@Localizer["Registration Date"]</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach (var user in Model.ReferredUsers)
                                                        {
                                                            <tr>
                                                                <td>@user.Name @user.Surname</td>
                                                                <td>@user.Email</td>
                                                                <td>@DateTimeFormatHelper.FormatForDisplay(user.CrDate)</td>
                                                            </tr>
                                                        }
                                                    </tbody>
                                                </table>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                @Localizer["You haven't referred any users yet. Share your referral code to start referring friends!"]
                                            </div>
                                        }
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h3 class="card-title">@Localizer["Referral Hierarchy"] - @Localizer["Who Referred Whom"]</h3>
                                    </div>
                                    <div class="card-body">
                                        @if (Model.ReferralHierarchy.Any())
                                        {
                                            <div class="mb-3">
                                                <p class="text-muted">@Localizer["This tree shows the users you have referred and their referrals."]</p>
                                            </div>
                                            <div class="referral-tree">
                                                <ul class="tree">
                                                    @foreach (var item in Model.ReferralHierarchy)
                                                    {
                                                        @await Html.PartialAsync("_ReferralTreeItem", item, new ViewDataDictionary(ViewData) { { "ShowSearchButton", false } })
                                                    }
                                                </ul>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="alert alert-info">
                                                @Localizer["You haven't referred any users yet. Share your referral code to start referring friends!"]
                                            </div>
                                        }
                                    </div>
                                </div>
                            </div>
                            <!-- /.tab-pane -->
                        </div>
                        <!-- /.tab-content -->
                    </div><!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div><!-- /.container-fluid -->
</section>
<!-- /.content -->
@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <script>
        // Extend the global translations object with page-specific translations
        window.t = window.t || {};

        // Extend with profile page specific translations
        Object.assign(window.t, {
            // Copy functionality translations
            "Button not found for element": "@Localizer["Button not found for element"]",
            "Copied": "@Localizer["Copied"]",
            "Element not found": "@Localizer["Element not found"]",
            "Enter a valid IBAN": "@Localizer["Enter a valid IBAN"]",
            "Error copying to clipboard": "@Localizer["Error copying to clipboard"]",
            "Error validating IBAN": "@Localizer["Error validating IBAN"]",
            "Failed to copy": "@Localizer["Failed to copy"]",
            "Failed to copy text": "@Localizer["Failed to copy text"]",
            "Fallback copy error": "@Localizer["Fallback copy error"]",
            "Fallback copy failed": "@Localizer["Fallback copy failed"]",
            "Text copied to clipboard": "@Localizer["Text copied to clipboard"]",
            "Text copied to clipboard using fallback": "@Localizer["Text copied to clipboard using fallback"]",

            // Email verification translations
            "Sending": "@Localizer["Sending"]",
            "Send Verification Email": "@Localizer["Send Verification Email"]",
            "Success": "@Localizer["Success"]",
            "Error": "@Localizer["Error"]"
        });
    </script>



    <script src="~/site/pages/myaccount/profile/profile.js"></script>
}
